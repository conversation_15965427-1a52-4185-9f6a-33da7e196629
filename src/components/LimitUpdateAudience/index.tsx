import Modal from '@/components/Modal';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { RiErrorWarningLine } from '@remixicon/react';

interface ILimitUpdateAudience {
  open: boolean;
  setOpen: (open: boolean) => void;
}

export const LimitUpdateAudience: React.FC<ILimitUpdateAudience> = (
  props: ILimitUpdateAudience,
) => {
  const { open, setOpen } = props;
  const { t } = useTranslation();

  return (
    <Modal
      openModal={open}
      onCloseModal={setOpen}
      onOpenChange={setOpen}
      isCloseIcon={false}
      className="w-[430px]"
      titleAlign={'center'}
      title={
        <>
          <div className="m-auto w-fit">
            <RiErrorWarningLine size={80} color={'#F53E3E'} />
          </div>
          <p className="text-xl text-primary font-semibold text-center">
            {t('audience.updateLimitReached')}
          </p>
        </>
      }
    >
      <div className="flex flex-col items-center gap-4 p-4">
        <p className="text-center text-sm text-secondary max-w-md">
          {t('audience.updateLimitMessage')}
        </p>
        <Button
          onClick={() => setOpen(false)}
          className="px-6 py-2 rounded-xl"
          variant={'primary'}
        >
          {t('common.button.ok')}
        </Button>
      </div>
    </Modal>
  );
};
