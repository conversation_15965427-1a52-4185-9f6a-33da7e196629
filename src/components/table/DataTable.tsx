import React, { CSSProperties, useEffect, useRef, useState } from 'react';
import {
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getExpandedRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
  VisibilityState,
} from '@tanstack/react-table';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table';
import ToolbarTable from './ToolbarTable';
import { cn } from '@/lib/utils';
import PaginationTable from './PaginationTable';
import { TNewCol, TTableProps } from '@/types/table';
import FilterBarContainer from '@/components/ContactList/FilterBarContainer';
import { RiLoader2Line } from '@remixicon/react';
import { useCallContext } from '@/pages/context/CallContext';
import { SortIcon } from '@/assets/SortIcon';
import { NoData } from '@/components/NoData';

const DataTable = <T extends object>({
  data,
  columns,
  isToolbar = true,
  isPerPageChange = true,
  searchColumn,
  body,
  searchPlaceholder,
  loading = false,
  total,
  renderSubComponent,
  getRowCanExpand,
  getRowsSelected,
  getSortingChange,
  onResetFilter,
  isSelectCol,
  filter,
  pagination,
  setPagination,
  className,
  classNameHeader,
  containerClassName,
  isShowFilter,
  searchPlaceHolder,
  scrollRef,
  isSingle = false,
  notfound,
}: TTableProps<T>) => {
  const {
    isFilter = false,
    isSearch = false,
    leftNodeFilter,
    centerNodeFilter,
    rightNodeFilter,
    bottomNodeFilter,
    filterNode,
  } = filter || {};
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});
  const [columnPinning, setColumnPinning] = useState({});
  const { contactCall } = useCallContext();

  const prevSelectionRef = useRef<Record<string, boolean>>({});

  const table = useReactTable({
    data,
    columns,
    manualPagination: true,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    onColumnPinningChange: setColumnPinning,
    getExpandedRowModel: getExpandedRowModel(),
    getRowCanExpand,
    columnResizeMode: 'onChange',
    columnResizeDirection: 'ltr',
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      columnPinning,
      pagination: {
        pageIndex: pagination ? pagination.currentPage - 1 : 1,
        pageSize: pagination ? pagination.pageSize : 10,
      },
    },
  });

  const handleGetCol = (column: TNewCol<T>) => {
    return column.columnDef;
  };

  const getCommonPinningStyles = (
    column: TNewCol<T>,
    index: number,
    isHeader: boolean,
  ): CSSProperties => {
    const columnTable = columns[index];
    const meta = columnTable.meta as { sticky?: string | undefined; position?: number | undefined };
    const sticky = meta?.sticky as 'left' | 'right' | undefined;
    const widthColumn =
      data.length > 0 || !loading
        ? body
          ? column.getSize()
          : data.length > 0
            ? column.getSize()
            : 'auto'
        : 'auto';

    const shadow = '1px 0px 0px 0px #E1E2E3 inset, 0px -1px 0px 0px #E1E2E3 inset';
    const shadowHeader =
      isHeader && table.getRowModel().rows?.length === 0 ? '1px 0px 0px 0px #E1E2E3 inset' : shadow;
    return {
      left: sticky === 'left' ? `${meta.position ?? 0}px` : undefined,
      right: sticky === 'right' ? `${meta.position ?? 0}px` : undefined,
      position: sticky ? 'sticky' : 'relative',
      boxShadow:
        sticky === 'left'
          ? '-' + shadowHeader
          : sticky === 'right'
            ? shadow
            : table.getRowModel().rows?.length !== 0
              ? '0px -1px 0px 0px #E1E2E3 inset'
              : '',
      width: widthColumn,
      minWidth: widthColumn,
      maxWidth: widthColumn,
      zIndex: sticky ? (isHeader ? 2 : 1) : 0,
    };
  };

  const getStickyColumnClass = (isLastRow: boolean): string => {
    const classes = [];
    if (!isLastRow) {
      classes.push('border-none border-[#E1E2E3]');
    }

    return classes.join(' ');
  };

  const handleCheckIsSort = (column: TNewCol<T>): boolean => {
    return handleGetCol(column)?.enableSorting || false;
  };

  const handleCheckIsAction = (column: TNewCol<T>): boolean => {
    return handleGetCol(column)?.accessorKey === 'action';
  };

  const viewIconSort = (column: TNewCol<T>) => {
    return (
      {
        asc: <SortIcon size={28} className="mr-auto" colorAsc={'#20232c'} colorDesc={'#9AA3C4'} />,
        desc: <SortIcon size={28} className="mr-auto" colorDesc={'#20232c'} colorAsc={'#9AA3C4'} />,
      }[column.getIsSorted() as string] ?? (
        <SortIcon size={28} className="mr-auto opacity-50 hover:opacity-100" />
      )
    );
  };

  useEffect(() => {
    if (isSingle) {
      const selectedKeys = Object.keys(rowSelection);

      const prevKeys = Object.keys(prevSelectionRef.current);

      if (selectedKeys.length > 1) {
        let newKey = selectedKeys.find((key) => !prevKeys.includes(key));

        if (!newKey) {
          newKey = selectedKeys[selectedKeys.length - 1];
        }

        setRowSelection({
          [newKey]: true,
        });
      }

      prevSelectionRef.current = { ...rowSelection };

      if (getRowsSelected) {
        const rowSelected = table.getGroupedSelectedRowModel().rows.map((row) => row.original);
        getRowsSelected(rowSelected || []);
      }
    } else {
      if (getRowsSelected) {
        const rowSelected = table.getGroupedSelectedRowModel().rows.map((row) => row.original);
        getRowsSelected(rowSelected || []);
      }
    }
  }, [rowSelection, getRowsSelected, table, isSingle]);

  useEffect(() => {
    if (getSortingChange) {
      getSortingChange(sorting);
    }
  }, [sorting, getSortingChange]);

  const handleResetFilter = () => {
    resetTable();
  };

  useEffect(() => {
    if (onResetFilter) {
      onResetFilter(handleResetFilter);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [onResetFilter]);

  const resetTable = () => {
    table.resetExpanded();
    table.resetRowSelection();
  };

  return (
    <>
      {isToolbar && (
        <ToolbarTable
          isSelectCol={isSelectCol}
          table={table}
          searchColumn={searchColumn}
          searchPlaceholder={searchPlaceholder}
        />
      )}
      {isShowFilter && (
        <FilterBarContainer
          isFilter={isFilter}
          isSearch={isSearch}
          leftNode={leftNodeFilter}
          centerNode={centerNodeFilter}
          rightNode={rightNodeFilter}
          bottomNode={bottomNodeFilter}
          filterNode={filterNode}
          searchPlaceHolder={searchPlaceHolder}
        />
      )}

      <Table
        className={cn(
          className,
          loading && 'overflow-hidden min-h-[300px]',
          table.getRowModel().rows?.length === 0 ? (className ?? 'h-full') : '',
        )}
        classTable={cn(table.getRowModel().rows?.length === 0 && 'h-full')}
        containerClassName={containerClassName}
        scrollRef={scrollRef}
      >
        <TableHeader className="text-primary">
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow
              className={cn(`sticky top-0 z-10 border-tertiary`, classNameHeader)}
              key={headerGroup.id}
            >
              {headerGroup.headers.map((header, index) => {
                const column = header.column as TNewCol<T>;
                const checkTitle = header.isPlaceholder;
                const minWidth = column.columnDef.minSize;
                const enableResizing =
                  column.columnDef.enableResizing !== undefined
                    ? column.columnDef.enableResizing
                    : true;
                const titleColumn = flexRender(column.columnDef.header, header.getContext());
                const isSort = handleCheckIsSort(column);
                return (
                  <TableHead
                    key={header.id}
                    className={cn(
                      `group text-big360Color-neutral-700 bg-big360Color-neutral-50 font-medium capitalize pr-4 py-0 pl-2 `,
                    )}
                    colSpan={header.colSpan}
                    style={{
                      ...getCommonPinningStyles(column, index, true),
                      minWidth: table.getRowModel().rows?.length === 0 ? 'auto' : minWidth,
                    }}
                  >
                    <div className="flex justify-between">
                      {checkTitle ? null : isSort ? (
                        <div
                          className={cn(
                            column.getCanSort() && 'flex items-center gap-1 w-full',
                            handleCheckIsAction(column)
                              ? 'flex-row-reverse justify-between'
                              : 'justify-between',
                            isSort && 'cursor-pointer select-none',
                          )}
                        >
                          {titleColumn}
                          <button
                            onClick={column.getToggleSortingHandler()}
                            className={cn(handleCheckIsAction(column) && 'mr-auto')}
                          >
                            {isSort ? viewIconSort(column) : null}
                          </button>
                        </div>
                      ) : (
                        titleColumn
                      )}
                      {enableResizing && (
                        <div
                          {...{
                            onDoubleClick: () => header.column.resetSize(),
                            onMouseDown: header.getResizeHandler(),
                            onTouchStart: header.getResizeHandler(),
                            className: cn(
                              'resizer isResizing cursor-grab min-h-[1px] h-full w-[3px] absolute top-0 right-0 group-hover:bg-[#A7AAB1]',
                              table.getState().columnSizingInfo.deltaOffset ? 'bg-[#A7AAB1]' : '',
                            ),
                          }}
                        />
                      )}
                    </div>
                  </TableHead>
                );
              })}
            </TableRow>
          ))}
        </TableHeader>
        {(body && body(table)) ?? (
          <TableBody
            className={cn(
              '[&_tr:first-child]:border-t-[0] min-h-[400px]',
              loading && 'max-h-[640px]',
            )}
          >
            {loading && (
              <>
                {table.getRowModel().rows?.length === 0 && (
                  <TableRow className="min-h-[400px] h-full max-h-[640px]" />
                )}
                <TableRow className="absolute z-20 flex items-center justify-center w-full h-full m-auto bg-muted/50 top-0 left-0">
                  <TableCell colSpan={columns.length} className="text-center">
                    <div className="flex items-center justify-center p-4 pl-2">
                      <RiLoader2Line className="animate-spin" size={24} />
                    </div>
                  </TableCell>
                </TableRow>
              </>
            )}
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => {
                const phoneNumberIndex = row
                  .getVisibleCells()
                  .findIndex((cell) => cell.column.id === 'phone_number');
                return (
                  <React.Fragment key={row.id}>
                    <TableRow
                      data-state={row.getIsSelected() && 'selected'}
                      className="border-b-0 group"
                    >
                      {row.getVisibleCells().map((cell, index) => {
                        const column = cell.column as TNewCol<T>;
                        const isLastRow = index === table.getRowModel().rows.length - 1;

                        const rowData = row.original as {
                          [key: string]: string;
                        };
                        return (
                          <TableCell
                            key={cell.id}
                            className={cn(
                              'p-4 pl-2 bg-popover font-normal text-sm text-secondary text-left group-hover:bg-hover-table',
                              phoneNumberIndex >= 0 &&
                                rowData[row.getVisibleCells()[phoneNumberIndex].column.id] ===
                                  contactCall?.phone_number &&
                                'bg-[#EEF9FF] group-hover:bg-[#EEF9FF]',
                              getStickyColumnClass(isLastRow),
                            )}
                            style={{ ...getCommonPinningStyles(column, index, false) }}
                          >
                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                          </TableCell>
                        );
                      })}
                    </TableRow>
                    {row.getIsExpanded() && renderSubComponent ? (
                      <TableRow>
                        <TableCell colSpan={columns.length} className="p-0">
                          {renderSubComponent(row.original)}
                        </TableCell>
                      </TableRow>
                    ) : (
                      <></>
                    )}
                  </React.Fragment>
                );
              })
            ) : (
              <></>
            )}
            {!loading && table.getRowModel().rows?.length === 0 ? (
              (notfound ?? (
                <TableRow className="hover:bg-transparent">
                  <TableCell
                    colSpan={columns.length}
                    className="h-full text-center hover:bg-transparent"
                  >
                    <NoData className="min-h-[350px]" />
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <></>
            )}
          </TableBody>
        )}
      </Table>
      {pagination && setPagination && data.length > 0 ? (
        <PaginationTable
          limit={pagination.pageSize}
          page={pagination.currentPage}
          setPage={(page) => {
            setPagination({
              ...pagination,
              currentPage: page,
            });
            resetTable();
          }}
          setLimit={(limit) => {
            setPagination({
              ...pagination,
              pageSize: limit,
              currentPage: 1,
            });
            resetTable();
          }}
          isChangePerPage={isPerPageChange}
          totalPages={Math.ceil(total / pagination.pageSize)}
        />
      ) : (
        <></>
      )}
    </>
  );
};

export default DataTable;
