import { useEffect, useState } from 'react';
import DataTable from '@/components/table/DataTable';
import { TFilterAudienceTiktok } from '@/types/facebook';
import { ICustomAudienceResponse } from '@/types/tiktok';
import FilterPanel from '@/pages/TiktokAds/components/FilterPanel';
import audienceCol from '@/pages/TiktokAds/components/Column/audienceCol';
import { useTiktokContext } from '@/pages/TiktokAds/context/TiktokAuthContext';
import { formatDateYYYYMMDD } from '@/utils/helper';
import { IAudienceDetail } from '@/types/audience';
import { get } from '@/apis/apiHelper';
import { ENDPOINTS } from '@/apis/endpoints';

interface ICustomAudiences {
  filterPayload: TFilterAudienceTiktok;
  setFilterPayload: (value: TFilterAudienceTiktok) => void;
  loading?: boolean;
  tiktokCustomAudience: ICustomAudienceResponse;
  onOpenHistoryModal: (detail: IAudienceDetail) => void;
  onOpenUpdateModal: (detail: IAudienceDetail) => void;
}

const CustomTiktokAudiences = ({
  filterPayload,
  setFilterPayload,
  loading,
  tiktokCustomAudience,
  onOpenHistoryModal,
  onOpenUpdateModal,
}: ICustomAudiences) => {
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const listAudienceTiktok = tiktokCustomAudience.items ?? [];
  const { adsAccount } = useTiktokContext();

  const [idActive, setIdActive] = useState<number>(0);
  const [listAudiences, setListAudiences] = useState<IAudienceDetail[]>(listAudienceTiktok);

  useEffect(() => {
    if (!!listAudienceTiktok.length) {
      setListAudiences(listAudienceTiktok);
    }
  }, [listAudienceTiktok]);

  useEffect(() => {
    if (idActive > 0 && listAudiences.find((item) => item.total_records === 0 || item.status === 'PENDING')) {
      get({
        endpoint: ENDPOINTS.custom_audience.detail(idActive.toString()),
      }).then((res) => {
        const dataAudience = res?.data?.data as unknown as IAudienceDetail;
        setListAudiences((prev) =>
          prev.map((item) =>
            item.job_id === dataAudience.job_id? dataAudience : item,
          ),
        );
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [idActive]);

  return (
    <div className="flex flex-col">
      <FilterPanel
        setFilterPayload={(value) => {
          setFilterPayload({
            ...filterPayload,
            ...value,
            date_created_from: formatDateYYYYMMDD(value?.date_created_from ?? '', '-'),
            date_created_to: formatDateYYYYMMDD(value?.date_created_to ?? '', '-'),
          });
        }}
        filterPayload={filterPayload}
      />
      <DataTable
        className="h-[468px]"
        data={tiktokCustomAudience.items}
        columns={audienceCol({
          act: adsAccount?.ad_account_id || '',
          setIdActive,
          onOpenHistoryModal,
          onOpenUpdateModal
        })}
        total={tiktokCustomAudience.count}
        loading={loading}
        pagination={{ pageSize: filterPayload.limit, currentPage: filterPayload.page }}
        setPagination={(value) => {
          setFilterPayload({ ...filterPayload, limit: value.pageSize, page: value.currentPage });
        }}
      />
    </div>
  );
};
export default CustomTiktokAudiences;
