import { formatDateTime } from '@/utils/helper';
import { ColumnDef } from '@tanstack/react-table';
import { t } from 'i18next';
import { DetailAudience } from '@/components/DetailAudience';
import { IAudienceDetail } from '@/types/audience';

type Props = {
  act: string;
  setIdActive: (id: number) => void;
  onOpenHistoryModal?: (detail: IAudienceDetail) => void;
  onOpenUpdateModal?: (detail: IAudienceDetail) => void;
};

const audienceCol = ({
  act,
  setIdActive,
  onOpenHistoryModal,
  onOpenUpdateModal,
}: Props): ColumnDef<IAudienceDetail>[] => {
  return [
    {
      accessorKey: 'audience_name',
      header: () => t('common.facebookAds.customAudience'),
      cell: ({ row }) => {
        return (
          <DetailAudience
            detail={row.original}
            setIdActive={setIdActive}
            act={act}
            type={'TIKTOK'}
            onOpenHistoryModal={onOpenHistoryModal}
            onOpenUpdateModal={onOpenUpdateModal}
          />
        );
      },
      size: 270,
      meta: {
        sticky: 'left',
      },
    },
    {
      accessorKey: 'segment.name',
      header: () => t('segment.title'),
      cell: ({ row }) => {
        return (
          <div className="flex items-center gap-2">
            <span className="line-clamp-1">{row.original.segment_name}</span>
          </div>
        );
      },
      size: 200,
    },
    {
      accessorKey: 'number_contact',
      header: () => <div className="text-right w-full">{t('segment.totalContacts')}</div>,
      cell: ({ row }) => {
        return (
          <div className="text-right w-full">
            {Number(row.original.total_records).toLocaleString()}
          </div>
        );
      },
      size: 150,
    },
    {
      accessorKey: 'date_created',
      header: () => <div className="text-right w-full">{t('common.lastUpdate')}</div>,
      cell: ({ row }) => {
        return (
          <div className="text-right">{formatDateTime(row.original.updated_at, '/', ':')}</div>
        );
      },
      size: 200,
    },
  ];
};

export default audienceCol;
