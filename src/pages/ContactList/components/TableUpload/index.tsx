/* eslint-disable react-hooks/exhaustive-deps */
import {
  type ColumnDef,
  type ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  type SortingState,
  useReactTable,
  type VisibilityState
} from '@tanstack/react-table';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { cn } from '@/lib/utils';
import PaginationTable from '@/components/table/PaginationTable';
import { useEffect, useState } from 'react';
import { NoData } from '@/components/NoData';

type Props<T = unknown> = {
  data: T[];
  columns: ColumnDef<T>[];
  className?: string;
  getItemHover?: (row: T | null) => void;
};

export function UploadTable<T = unknown>({ data, columns, className, getItemHover }: Props<T>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});
  const [pageSize, setPageSize] = useState(10);
  const [pageIndex, setPageIndex] = useState(0);
  const [hoveredRow, setHoveredRow] = useState<T | null>(null);

  useEffect(() => {
    if (pageSize === data.length && data.length > 0) {
      setPageSize(data.length);
    }
  }, [data, pageSize]);

  useEffect(() => {
    getItemHover?.(hoveredRow);
  }, [hoveredRow]);

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    onPaginationChange: (updater) => {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error
      const newState = updater(table.getState().pagination);
      setPageIndex(newState.pageIndex);
      setPageSize(newState.pageSize);
    },
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      pagination: {
        pageIndex,
        pageSize,
      },
    },
  });

  return (
    <div className={cn('space-y-4 overflow-auto', className)}>
      <Table className="border">
        <TableHeader className="bg-[#E2DAFF]">
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map((header) => {
                const minWidth = header.column.columnDef.minSize;
                return (
                  <TableHead
                    className="text-primary-crm font-semibold"
                    key={header.id}
                    style={{
                      width: header.column.getSize() + 'px',
                      minWidth: table.getRowModel().rows?.length === 0 ? 'auto' : minWidth,
                      maxWidth: header.column.getSize() + 'px',
                    }}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                );
              })}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows?.length ? (
            table.getRowModel().rows.map((row) => (
              <TableRow
                className="border-b last:border-b-0 hover:bg-secondary duration-150"
                key={row.id}
                data-state={row.getIsSelected() && 'selected'}
                onMouseEnter={() => setHoveredRow(row.original)}
                onMouseLeave={() => setHoveredRow(null)}
              >
                {row.getVisibleCells().map((cell) => (
                  <TableCell
                    style={{ width: cell.column.getSize() + 'px' }}
                    className={cn('p-3', cell.column.columnDef.meta)}
                    key={cell.id}
                  >
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))
          ) : (
            <TableRow className="hover:bg-transparent">
              <TableCell colSpan={columns.length} className="h-96 text-center hover:bg-transparent">
                <NoData />
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      <PaginationTable
        limit={pageSize}
        page={table.getState().pagination.pageIndex}
        setPage={(page) => table.setPageIndex(page)}
        setLimit={(limit) => table.setPageSize(limit)}
        isChangePerPage={true}
        totalPages={Math.ceil(data.length / pageSize)}
      />
    </div>
  );
}
